# field_mappings.json 配置文件创建流程分析

## 概述

`state/data/field_mappings.json` 配置文件是通过多个模块协作创建和维护的，主要在Excel数据导入过程中自动生成字段映射配置。

## 核心创建流程

### 1. 主要入口点

#### 1.1 Excel导入触发
- **文件**: `src/modules/data_import/multi_sheet_importer.py`
- **方法**: `_process_sheet_data()`
- **触发时机**: 用户导入Excel文件时

#### 1.2 字段映射生成
- **文件**: `src/modules/data_import/auto_field_mapping_generator.py`
- **方法**: `create_initial_field_mapping()`
- **功能**: 智能生成字段映射规则

#### 1.3 配置保存
- **文件**: `src/modules/data_import/config_sync_manager.py`
- **方法**: `save_complete_mapping()`
- **功能**: 将映射配置持久化到JSON文件

## 详细代码流程

### 第一步：Excel导入处理
```python
# src/modules/data_import/multi_sheet_importer.py
def _process_sheet_data(self, df, sheet_name, year=None, month=None):
    # 1. 获取Excel表头
    excel_headers = df.columns.tolist()
    
    # 2. 生成表名
    table_name = self._generate_table_name(sheet_name, year, month)
    
    # 3. 获取数据库字段名
    db_fields = self._get_database_field_names(table_name, excel_headers)
    
    # 4. 生成字段映射
    initial_mapping = self.auto_mapping_generator.create_initial_field_mapping(
        excel_headers, db_fields
    )
    
    # 5. 保存映射配置
    mapping_data = {
        "field_mappings": final_mapping,
        "original_excel_headers": dict(zip(db_fields, excel_headers)),
        "metadata": {
            "source": "excel_import",
            "auto_generated": True,
            "user_modified": False,
            "created_at": datetime.now().isoformat(),
            "sheet_name": sheet_name,
            "has_chinese_headers": self._has_chinese_headers(excel_headers)
        }
    }
    
    self.config_sync_manager.save_complete_mapping(table_name, mapping_data)
```

### 第二步：智能字段映射生成
```python
# src/modules/data_import/auto_field_mapping_generator.py
def create_initial_field_mapping(self, excel_headers, db_fields):
    """创建智能字段映射：数据库字段名 → Excel列名"""
    mapping = {}
    
    # 预定义的智能匹配规则
    field_patterns = {
        'employee_id': ['工号', '职工编号', '员工号', '编号'],
        'employee_name': ['姓名', '职工姓名', '员工姓名', '名字'],
        'department': ['部门', '部门名称', '所属部门'],
        # ... 更多映射规则
    }
    
    # 1. 智能匹配
    for db_field in db_fields:
        if db_field in field_patterns:
            patterns = field_patterns[db_field]
            for excel_header in excel_headers:
                if excel_header in patterns:
                    mapping[db_field] = excel_header
                    break
    
    # 2. 位置匹配（备用策略）
    # 3. 默认映射（最后兜底）
    
    return mapping
```

### 第三步：配置文件保存
```python
# src/modules/data_import/config_sync_manager.py
def save_complete_mapping(self, table_name, mapping_data):
    """保存完整的字段映射配置"""
    try:
        # 加载当前配置
        config = self._load_config_file()
        
        # 确保必要的键存在
        if "table_mappings" not in config:
            config["table_mappings"] = {}
        
        # 保存映射数据
        config["table_mappings"][table_name] = mapping_data
        config["last_updated"] = datetime.now().isoformat()
        
        # 保存到文件
        success = self._save_config_file(config)
        
        return success
    except Exception as e:
        self.logger.error(f"保存完整字段映射失败: {e}")
        return False

def _save_config_file(self, config):
    """保存配置文件"""
    try:
        # 确保目录存在
        config_dir = Path(self.config_path).parent
        config_dir.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        return True
    except Exception as e:
        self.logger.error(f"保存配置文件失败: {e}")
        return False
```

## 其他相关模块

### 1. 字段注册系统
- **文件**: `src/modules/format_management/field_registry.py`
- **功能**: 管理字段映射的加载、保存、验证
- **方法**: `load_mappings()`, `save_mappings()`

### 2. 专用字段映射生成器
- **文件**: `src/modules/data_import/specialized_field_mapping_generator.py`
- **功能**: 为特定表类型生成专用字段映射
- **方法**: `generate_mapping()`

### 3. 动态表管理器
- **文件**: `src/modules/data_storage/dynamic_table_manager.py`
- **功能**: 读取和应用字段映射配置
- **方法**: `_get_dynamic_field_mappings()`

### 4. 主界面保存
- **文件**: `src/gui/prototype/prototype_main_window.py`
- **功能**: 用户界面触发的字段映射保存
- **方法**: `_save_field_mappings_to_file()`

## 配置文件结构

生成的 `field_mappings.json` 包含以下主要结构：

```json
{
  "version": "2.0",
  "last_updated": "2025-08-01T18:21:39.940869",
  "global_settings": {
    "auto_generate_mappings": true,
    "enable_smart_suggestions": true,
    "save_edit_history": true,
    "preserve_chinese_headers": true
  },
  "table_mappings": {
    "表名": {
      "field_mappings": {
        "数据库字段名": "显示名称"
      },
      "original_excel_headers": {
        "数据库字段名": "原始Excel表头"
      },
      "metadata": {
        "source": "excel_import",
        "auto_generated": true,
        "user_modified": false,
        "created_at": "时间戳",
        "sheet_name": "工作表名",
        "has_chinese_headers": true
      }
    }
  }
}
```

## 关键代码文件详细分析

### 1. 主要创建文件

| 文件路径 | 主要功能 | 关键方法 | 作用 |
|---------|---------|---------|------|
| `src/modules/data_import/multi_sheet_importer.py` | Excel导入主流程 | `_process_sheet_data()` | 触发字段映射生成 |
| `src/modules/data_import/auto_field_mapping_generator.py` | 智能字段映射生成 | `create_initial_field_mapping()` | 生成字段映射规则 |
| `src/modules/data_import/config_sync_manager.py` | 配置持久化管理 | `save_complete_mapping()` | 保存到JSON文件 |
| `src/modules/format_management/field_registry.py` | 字段注册系统 | `save_mappings()` | 字段映射管理 |

### 2. 辅助支持文件

| 文件路径 | 功能 | 关键方法 |
|---------|------|---------|
| `src/modules/data_import/specialized_field_mapping_generator.py` | 专用字段映射 | `generate_mapping()` |
| `src/modules/data_storage/dynamic_table_manager.py` | 动态表管理 | `_get_dynamic_field_mappings()` |
| `src/gui/prototype/prototype_main_window.py` | 界面保存 | `_save_field_mappings_to_file()` |
| `src/gui/main_dialogs.py` | 对话框保存 | `_save_field_mapping_with_validation()` |

### 3. 配置读取文件

| 文件路径 | 功能 | 关键方法 |
|---------|------|---------|
| `src/core/unified_data_request_manager.py` | 统一数据请求 | `_load_field_mapping()` |
| `src/gui/prototype/widgets/column_sort_manager.py` | 列排序管理 | `_load_field_mappings_from_config()` |

## 创建触发场景

### 场景1：Excel文件导入
- **触发点**: 用户通过界面导入Excel文件
- **主要文件**: `multi_sheet_importer.py`
- **流程**: Excel导入 → 表头解析 → 字段映射生成 → 配置保存

### 场景2：字段映射编辑
- **触发点**: 用户在界面编辑字段显示名
- **主要文件**: `main_dialogs.py`, `prototype_main_window.py`
- **流程**: 用户编辑 → 验证映射 → 更新配置

### 场景3：系统初始化
- **触发点**: 系统启动时字段注册系统初始化
- **主要文件**: `field_registry.py`
- **流程**: 系统启动 → 加载配置 → 创建默认映射（如果不存在）

## 配置文件更新机制

### 自动更新
1. **Excel导入时**: 自动检测新字段并更新映射
2. **字段修复时**: 自动修复缺失或错误的映射
3. **模板应用时**: 应用预定义模板到新表

### 手动更新
1. **用户编辑**: 通过界面编辑字段显示名
2. **批量更新**: 通过配置管理器批量更新映射
3. **模板管理**: 创建和应用自定义模板

## 总结

`field_mappings.json` 文件主要通过以下代码文件创建：

1. **主流程**: `multi_sheet_importer.py` - Excel导入时触发
2. **映射生成**: `auto_field_mapping_generator.py` - 智能生成字段映射
3. **配置保存**: `config_sync_manager.py` - 持久化配置到JSON文件
4. **字段管理**: `field_registry.py` - 字段映射的管理和验证
5. **界面保存**: `prototype_main_window.py` - 用户界面触发的保存操作

整个流程是自动化的，在用户导入Excel文件时自动创建和更新字段映射配置。配置文件支持多种更新机制，包括自动检测、智能匹配、用户编辑等，确保字段映射的准确性和完整性。
