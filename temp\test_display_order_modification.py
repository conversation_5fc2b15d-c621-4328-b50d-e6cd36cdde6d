#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 display_order 修改是否生效
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_field_registry_display_order():
    """测试 FieldRegistry 中的 display_order 配置"""
    print("=== 测试 FieldRegistry display_order 配置 ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 初始化field_registry
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        
        # 获取 active_employees 的默认配置
        table_mappings = field_registry._field_mappings.get("table_mappings", {})
        
        # 查找 active_employees 配置
        active_config = None
        for table_key, config in table_mappings.items():
            if 'active_employees' in table_key:
                active_config = config
                print(f"找到表配置: {table_key}")
                break
        
        if active_config:
            display_order = active_config.get("display_order", [])
            print(f"display_order 字段数量: {len(display_order)}")
            print("display_order 内容:")
            for i, field in enumerate(display_order, 1):
                print(f"  {i:2d}. {field}")
            
            # 验证关键字段的位置
            expected_order = [
                "employee_id", "employee_name", "department", "employee_type", "employee_type_code",
                "position_salary_2025", "grade_salary_2025", "allowance", "balance_allowance",
                "basic_performance_2025", "performance_bonus_2025", "total_salary"
            ]
            
            print("\n验证前12个字段顺序:")
            for i, expected_field in enumerate(expected_order):
                if i < len(display_order):
                    actual_field = display_order[i]
                    status = "✓" if actual_field == expected_field else "✗"
                    print(f"  {i+1:2d}. {status} 期望: {expected_field}, 实际: {actual_field}")
                else:
                    print(f"  {i+1:2d}. ✗ 期望: {expected_field}, 实际: 缺失")
            
            return True
        else:
            print("未找到 active_employees 配置")
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_format_renderer_display_order():
    """测试 FormatRenderer 中的中文字段顺序"""
    print("\n=== 测试 FormatRenderer 中文字段顺序 ===")
    
    try:
        from src.modules.format_management.format_renderer import FormatRenderer
        
        # 创建 FormatRenderer 实例
        renderer = FormatRenderer()
        
        # 获取默认字段配置
        default_fields = renderer._get_default_display_fields('active_employees', [])
        
        print(f"默认中文字段数量: {len(default_fields)}")
        print("默认中文字段顺序:")
        for i, field in enumerate(default_fields, 1):
            print(f"  {i:2d}. {field}")
        
        # 验证关键字段的位置
        expected_chinese_order = [
            "工号", "姓名", "部门名称", "人员类别", "人员类别代码",
            "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴",
            "2025年基础性绩效", "2025年奖励性绩效预发", "应发工资"
        ]
        
        print("\n验证前12个中文字段顺序:")
        for i, expected_field in enumerate(expected_chinese_order):
            if i < len(default_fields):
                actual_field = default_fields[i]
                status = "✓" if actual_field == expected_field else "✗"
                print(f"  {i+1:2d}. {status} 期望: {expected_field}, 实际: {actual_field}")
            else:
                print(f"  {i+1:2d}. ✗ 期望: {expected_field}, 实际: 缺失")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_registry_default_fields():
    """测试 FieldRegistry 中的默认中文字段配置"""
    print("\n=== 测试 FieldRegistry 默认中文字段配置 ===")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 初始化field_registry
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        
        # 获取默认字段配置
        default_fields = field_registry._get_default_display_fields('active_employees')
        
        print(f"默认中文字段数量: {len(default_fields)}")
        print("默认中文字段顺序:")
        for i, field in enumerate(default_fields, 1):
            print(f"  {i:2d}. {field}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始测试 display_order 修改效果")
    print("=" * 60)
    
    # 测试1: FieldRegistry 的 display_order 配置
    test1_passed = test_field_registry_display_order()
    
    # 测试2: FormatRenderer 的中文字段顺序
    test2_passed = test_format_renderer_display_order()
    
    # 测试3: FieldRegistry 的默认中文字段配置
    test3_passed = test_field_registry_default_fields()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"  FieldRegistry display_order 测试: {'通过' if test1_passed else '失败'}")
    print(f"  FormatRenderer 中文字段顺序测试: {'通过' if test2_passed else '失败'}")
    print(f"  FieldRegistry 默认中文字段测试: {'通过' if test3_passed else '失败'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n✓ 所有测试通过！display_order 修改已生效")
    else:
        print("\n✗ 部分测试失败，需要进一步检查")
