#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式渲染器

负责实际的数据格式化和渲染，将原始数据转换为用户友好的显示格式。
根据字段类型和格式配置，对数据进行类型转换、格式化和验证。

主要功能:
- 数据类型转换（字符串、整型、浮点型、货币等）
- 格式化渲染（千分位分隔符、小数位数、货币符号等）
- 空值处理和默认值设置
- 数据验证和错误处理
- 批量数据处理优化

支持的数据类型:
- currency: 货币格式（¥1,234.56）
- integer: 整型格式（1,234）
- float: 浮点型格式（1,234.56）
- percentage: 百分比格式（12.3%）
- date: 日期格式（2025年07月18日）
- string: 字符串格式

创建时间: 2025-07-18
作者: 统一格式管理系统重构团队
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, date
import re
import logging
import time
from decimal import Decimal, InvalidOperation

# 导入项目内部模块
try:
    from src.utils.log_config import setup_logger
except ImportError:
    # 如果无法导入，使用标准日志
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

from .format_config import FormatConfig
from .field_registry import FieldRegistry


class FormatRenderer:
    """
    格式渲染器
    
    负责将原始数据根据配置的格式规则转换为用户友好的显示格式。
    处理各种数据类型的格式化、验证和错误处理。
    """
    
    def __init__(self, format_config: FormatConfig, field_registry: FieldRegistry):
        """
        初始化格式渲染器
        
        Args:
            format_config: 格式配置管理器
            field_registry: 字段注册系统
        """
        self.logger = setup_logger("format_management.format_renderer")
        
        self.format_config = format_config
        self.field_registry = field_registry
        
        # 格式化缓存
        self._format_cache = {}
        
        # 错误统计
        self._error_count = 0
        self._warning_count = 0
        
        self.logger.info("🎨 [格式渲染] 格式渲染器初始化完成")
    
    # ================== 主要渲染接口 ==================
    
    def render_dataframe(self, df: pd.DataFrame, table_type: str) -> pd.DataFrame:
        """
        渲染整个DataFrame

        Args:
            df: 原始DataFrame
            table_type: 表格类型

        Returns:
            格式化后的DataFrame
        """
        # 🔧 [增强日志] 生成操作ID用于跟踪
        operation_id = f"render_{int(time.time() * 1000)}"

        # 🔧 [增强日志] 详细的输入验证
        if df is None:
            self.logger.error(f"🔧 [格式修复-{operation_id}] DataFrame为None")
            return pd.DataFrame()

        if df.empty:
            self.logger.warning(f"🔧 [格式修复-{operation_id}] DataFrame为空")
            return df

        # 🔧 [增强日志] 记录详细的输入状态
        input_info = {
            'operation_id': operation_id,
            'rows': len(df),
            'columns': len(df.columns),
            'table_type': table_type,
            'column_names': list(df.columns)[:10],  # 只记录前10个列名
            'memory_usage_mb': round(df.memory_usage(deep=True).sum() / 1024 / 1024, 2)
        }
        self.logger.info(f"🎯 [格式渲染-{operation_id}] 开始渲染: {input_info}")

        # 🔧 [异常恢复] 备份原始数据
        original_df = df.copy()

        try:
            # 创建副本以避免修改原始数据
            formatted_df = df.copy()
            
            # 🎯 [用户需求] 获取隐藏字段并从DataFrame中移除
            hidden_fields = self.field_registry.get_hidden_fields(table_type)
            self.logger.info(f"🎯 [格式渲染] 隐藏字段配置: {hidden_fields}")
            if hidden_fields:
                # 移除隐藏字段
                columns_to_drop = [col for col in hidden_fields if col in formatted_df.columns]
                if columns_to_drop:
                    formatted_df = formatted_df.drop(columns=columns_to_drop)
                    self.logger.info(f"🎯 [格式渲染] 已隐藏字段: {columns_to_drop}")
                else:
                    self.logger.info(f"🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）")
            
            # 获取字段类型映射
            field_types = self.field_registry.get_table_field_types(table_type)
            
            # 逐列格式化
            for column in formatted_df.columns:
                field_type = field_types.get(column, 'string')
                
                try:
                    # 🔧 [P2-修复] 获取字段的中文显示名称，用于特殊字段识别
                    display_name = self.field_registry.get_display_name(column, table_type)
                    formatted_df[column] = self.render_column(
                        formatted_df[column], 
                        field_type, 
                        column, 
                        table_type,
                        display_name  # 传递显示名称，确保人员类别代码等特殊字段能正确处理
                    )
                except Exception as e:
                    self.logger.error(f"🎨 [格式渲染] 列格式化失败 {column}: {e}")
                    self._error_count += 1
                    # 保留原始数据
                    continue
            
            # 🎯 [用户需求] 按display_order重新排列列的顺序 + 🔧 [智能修复] 智能降级处理
            display_fields = self.field_registry.get_display_fields(table_type)
            self.logger.info(f"🔧 [DEBUG] table_type={table_type}, display_fields={len(display_fields) if display_fields else 0}个字段")

            # 🔧 [关键修复] 智能字段处理逻辑
            final_display_fields = self._get_smart_display_fields(display_fields, table_type, formatted_df.columns)

            if final_display_fields:
                # 只保留实际存在的列
                existing_display_fields = [field for field in final_display_fields if field in formatted_df.columns]
                self.logger.info(f"🔧 [DEBUG] formatted_df.columns={len(formatted_df.columns)}个, existing_display_fields={len(existing_display_fields)}个")

                if existing_display_fields:
                    formatted_df = formatted_df[existing_display_fields]
                    self.logger.info(f"🎯 [格式渲染] 已按智能字段配置排列: {len(existing_display_fields)}个字段")
                else:
                    # 🔧 [最终降级] 使用智能推断的字段顺序
                    smart_fields = self._infer_display_fields_from_data(formatted_df, table_type)
                    if smart_fields:
                        formatted_df = formatted_df[smart_fields]
                        self.logger.info(f"🔧 [智能修复] 使用推断字段顺序: {len(smart_fields)}个字段")
                    else:
                        self.logger.warning(f"🔧 [最终降级] 保持原始列顺序: {len(formatted_df.columns)}列")
            else:
                # 🔧 [异常情况] 所有智能处理都失败时的最终降级
                self.logger.error(f"🔧 [异常降级] 所有智能处理失败，保持原始数据结构")
                # 保持原始数据，但记录问题以便后续分析
                self._record_format_failure(table_type, formatted_df.columns)
            
            # 🔧 [增强日志] 记录成功完成状态
            output_info = {
                'operation_id': operation_id,
                'rows': len(formatted_df),
                'columns': len(formatted_df.columns),
                'processing_time_ms': round((time.time() * 1000) - int(operation_id.split('_')[1]), 2)
            }
            self.logger.info(f"🎯 [格式渲染-{operation_id}] 渲染完成: {output_info}")
            return formatted_df

        except Exception as e:
            # 🔧 [异常恢复] 详细的异常处理和恢复
            error_context = {
                'operation_id': operation_id,
                'table_type': table_type,
                'original_shape': (len(original_df), len(original_df.columns)),
                'error_type': type(e).__name__,
                'error_message': str(e)
            }
            self.logger.error(f"🔧 [格式修复-{operation_id}] DataFrame渲染失败: {error_context}", exc_info=True)

            # 🔧 [用户友好] 尝试提供有意义的错误反馈
            self._provide_user_friendly_error_feedback(error_context)

            # 🔧 [格式修复] 返回原始数据作为降级处理
            self.logger.info(f"🔧 [异常恢复-{operation_id}] 返回原始数据作为降级处理")
            return original_df

    def _get_default_display_fields(self, table_type: str, available_columns: List[str]) -> List[str]:
        """
        🔧 [格式修复] 获取默认显示字段配置

        Args:
            table_type: 表类型
            available_columns: 可用的列名列表

        Returns:
            默认字段列表
        """
        try:
            # 定义各表类型的默认字段顺序
            default_configs = {
                'active_employees': [
                    "工号", "姓名", "部门名称", "人员类别", "人员类别代码",
                    "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴",
                    "2025年基础性绩效", "2025年奖励性绩效预发", "应发工资",
                    "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴",
                    "2025公积金", "代扣代存养老保险", "补发", "借支",
                    "年份", "月份", "序号"
                ],
                'a_grade_employees': [
                    "工号", "姓名", "部门名称", "人员类别代码", "人员类别",
                    "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴",
                    "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补",
                    "2025年奖励性绩效预发", "补发", "借支", "应发工资",
                    "2025公积金", "保险扣款", "代扣代存养老保险"
                ],
                'retired_employees': [
                    "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴",
                    "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费",
                    "增发一次性生活补贴", "补发", "合计", "借支", "备注"
                ],
                'pension_employees': [
                    "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费",
                    "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴",
                    "住房补贴", "增资预付", "补发", "借支", "应发工资",
                    "公积", "保险扣款", "备注"
                ]
            }

            # 获取对应的默认配置
            default_fields = default_configs.get(table_type, [])

            if default_fields:
                # 只返回实际存在的字段
                existing_fields = [field for field in default_fields if field in available_columns]
                self.logger.info(f"🔧 [格式修复] 默认配置匹配: {len(existing_fields)}/{len(default_fields)} 字段")
                return existing_fields
            else:
                # 如果没有预定义配置，返回前20个可用列（避免过多列）
                limited_columns = list(available_columns)[:20]
                self.logger.info(f"🔧 [格式修复] 使用前{len(limited_columns)}列作为默认配置")
                return limited_columns

        except Exception as e:
            self.logger.error(f"🔧 [格式修复] 获取默认字段配置失败: {e}")
            return []

    def _get_smart_display_fields(self, display_fields: List[str], table_type: str, available_columns: List[str]) -> List[str]:
        """
        🔧 [智能修复] 获取智能显示字段配置

        Args:
            display_fields: 原始显示字段配置
            table_type: 表类型
            available_columns: 可用的列名列表

        Returns:
            智能处理后的字段列表
        """
        try:
            # 1. 如果原始配置有效，直接使用
            if display_fields:
                valid_fields = [field for field in display_fields if field in available_columns]
                if valid_fields:
                    self.logger.info(f"🔧 [智能修复] 使用原始配置: {len(valid_fields)}个有效字段")
                    return display_fields
                else:
                    self.logger.warning(f"🔧 [智能修复] 原始配置字段全部无效，尝试智能推断")

            # 2. 尝试从数据结构推断显示字段
            inferred_fields = self._infer_display_fields_from_data_structure(available_columns, table_type)
            if inferred_fields:
                self.logger.info(f"🔧 [智能修复] 从数据结构推断字段: {len(inferred_fields)}个")
                return inferred_fields

            # 3. 使用预定义的默认配置
            default_fields = self._get_default_display_fields(table_type, available_columns)
            if default_fields:
                self.logger.info(f"🔧 [智能修复] 使用默认配置: {len(default_fields)}个字段")
                return default_fields

            # 4. 最终降级：使用智能排序的所有字段
            smart_ordered_fields = self._get_smart_ordered_fields(available_columns)
            self.logger.info(f"🔧 [智能修复] 使用智能排序字段: {len(smart_ordered_fields)}个")
            return smart_ordered_fields

        except Exception as e:
            self.logger.error(f"🔧 [智能修复] 智能字段处理失败: {e}")
            return list(available_columns)  # 最终降级

    def _infer_display_fields_from_data_structure(self, columns: List[str], table_type: str) -> List[str]:
        """
        🔧 [智能修复] 从数据结构推断显示字段

        Args:
            columns: 可用列名
            table_type: 表类型

        Returns:
            推断的字段列表
        """
        try:
            # 定义重要字段的优先级模式
            priority_patterns = {
                'high': ['工号', '姓名', '人员代码', '部门', '类别'],
                'medium': ['工资', '津贴', '绩效', '补贴', '应发'],
                'low': ['年份', '月份', '序号', '备注']
            }

            # 按优先级分类字段
            high_priority = []
            medium_priority = []
            low_priority = []
            other_fields = []

            for column in columns:
                categorized = False
                for priority, patterns in priority_patterns.items():
                    if any(pattern in column for pattern in patterns):
                        if priority == 'high':
                            high_priority.append(column)
                        elif priority == 'medium':
                            medium_priority.append(column)
                        else:
                            low_priority.append(column)
                        categorized = True
                        break

                if not categorized:
                    other_fields.append(column)

            # 组合最终字段列表
            result = high_priority + medium_priority + other_fields + low_priority

            # 限制字段数量，避免显示过多列
            max_fields = 25
            if len(result) > max_fields:
                result = result[:max_fields]
                self.logger.info(f"🔧 [智能修复] 字段数量限制为{max_fields}个")

            return result

        except Exception as e:
            self.logger.error(f"🔧 [智能修复] 数据结构推断失败: {e}")
            return []

    def _get_smart_ordered_fields(self, columns: List[str]) -> List[str]:
        """
        🔧 [智能修复] 获取智能排序的字段列表

        Args:
            columns: 原始列名列表

        Returns:
            智能排序后的字段列表
        """
        try:
            # 按字段重要性和常见顺序排序
            def field_sort_key(field_name: str) -> tuple:
                # 返回排序权重 (优先级, 字母顺序)
                if any(key in field_name for key in ['工号', '人员代码']):
                    return (0, field_name)
                elif '姓名' in field_name:
                    return (1, field_name)
                elif '部门' in field_name:
                    return (2, field_name)
                elif '类别' in field_name:
                    return (3, field_name)
                elif any(key in field_name for key in ['工资', '津贴', '绩效']):
                    return (4, field_name)
                elif any(key in field_name for key in ['补贴', '应发']):
                    return (5, field_name)
                elif any(key in field_name for key in ['年份', '月份']):
                    return (9, field_name)
                else:
                    return (6, field_name)

            sorted_fields = sorted(columns, key=field_sort_key)
            return sorted_fields

        except Exception as e:
            self.logger.error(f"🔧 [智能修复] 智能排序失败: {e}")
            return list(columns)

    def _record_format_failure(self, table_type: str, columns: List[str]):
        """
        🔧 [智能修复] 记录格式化失败信息，用于后续分析

        Args:
            table_type: 表类型
            columns: 列名列表
        """
        try:
            failure_info = {
                'table_type': table_type,
                'column_count': len(columns),
                'columns': list(columns)[:10],  # 只记录前10个列名
                'timestamp': time.time()
            }

            # 记录到日志中，便于后续分析
            self.logger.error(f"🔧 [格式失败记录] {failure_info}")

            # 可以考虑将失败信息写入专门的文件，用于后续优化

        except Exception as e:
            self.logger.error(f"🔧 [智能修复] 记录格式化失败信息时出错: {e}")

    def _infer_display_fields_from_data(self, df: pd.DataFrame, table_type: str) -> List[str]:
        """
        🔧 [智能修复] 从DataFrame推断显示字段（兼容性方法）

        Args:
            df: DataFrame对象
            table_type: 表类型

        Returns:
            推断的字段列表
        """
        return self._infer_display_fields_from_data_structure(df.columns.tolist(), table_type)

    def _provide_user_friendly_error_feedback(self, error_context: dict):
        """
        🔧 [用户友好] 提供用户友好的错误反馈

        Args:
            error_context: 错误上下文信息
        """
        try:
            operation_id = error_context.get('operation_id', 'unknown')
            error_type = error_context.get('error_type', 'Unknown')
            table_type = error_context.get('table_type', 'unknown')

            # 根据错误类型提供具体的用户建议
            user_message = ""
            if error_type == "KeyError":
                user_message = "数据格式可能不匹配，请检查数据源是否正确"
            elif error_type == "ValueError":
                user_message = "数据值格式异常，可能包含无效的数据类型"
            elif error_type == "MemoryError":
                user_message = "数据量过大，建议分批处理或增加系统内存"
            elif error_type == "IndexError":
                user_message = "数据索引超出范围，可能是数据结构发生了变化"
            else:
                user_message = "数据处理过程中发生未知错误，系统将使用原始数据"

            # 记录用户友好的错误信息
            self.logger.warning(f"🔧 [用户提示-{operation_id}] {user_message}")

            # 可以考虑通过事件系统通知UI层显示用户友好的错误消息
            # 这里暂时只记录到日志中

        except Exception as feedback_error:
            self.logger.error(f"🔧 [错误反馈] 生成用户友好错误反馈失败: {feedback_error}")

    def _record_format_failure_with_context(self, table_type: str, columns: List[str], operation_id: str):
        """
        🔧 [增强日志] 记录格式化失败信息，包含详细上下文

        Args:
            table_type: 表类型
            columns: 列名列表
            operation_id: 操作ID
        """
        try:
            failure_info = {
                'operation_id': operation_id,
                'table_type': table_type,
                'column_count': len(columns),
                'columns': list(columns)[:15],  # 记录前15个列名
                'timestamp': time.time(),
                'failure_type': 'format_processing'
            }

            # 记录到日志中，便于后续分析
            self.logger.error(f"🔧 [格式失败记录-{operation_id}] {failure_info}")

            # 统计失败次数
            if not hasattr(self, '_format_failure_count'):
                self._format_failure_count = {}

            failure_key = f"{table_type}_{len(columns)}"
            self._format_failure_count[failure_key] = self._format_failure_count.get(failure_key, 0) + 1

            # 如果同类型失败次数过多，记录警告
            if self._format_failure_count[failure_key] > 3:
                self.logger.warning(f"🔧 [格式失败统计-{operation_id}] 表类型{table_type}格式化失败次数过多: {self._format_failure_count[failure_key]}次")

        except Exception as e:
            self.logger.error(f"🔧 [格式失败记录-{operation_id}] 记录格式化失败信息时出错: {e}")

    def render_column(self, 
                     column: pd.Series, 
                     field_type: str, 
                     field_name: str, 
                     table_type: str,
                     display_name: str = None) -> pd.Series:
        """
        渲染单个列
        
        Args:
            column: 原始列数据
            field_type: 字段类型
            field_name: 字段名
            table_type: 表格类型
            
        Returns:
            格式化后的列数据
        """
        try:
            # 获取格式配置 - 传递表类型信息
            format_config = self.format_config.get_format_rules(field_type, table_type)
            if not format_config:
                self.logger.warning(f"🎨 [格式渲染] 未找到格式配置: {field_type}")
                return column
            
            # 🔧 [P2-修复] 使用显示名称（如果有）来进行特殊字段识别
            effective_field_name = display_name or field_name
            
            # 根据字段类型选择渲染方法
            if field_type == 'currency':
                return self._render_currency_column(column, format_config, effective_field_name)
            elif field_type == 'integer':
                return self._render_integer_column(column, format_config, effective_field_name)
            elif field_type == 'float':
                return self._render_float_column(column, format_config, effective_field_name)
            elif field_type == 'percentage':
                return self._render_percentage_column(column, format_config, effective_field_name)
            elif field_type == 'date':
                return self._render_date_column(column, format_config, effective_field_name)
            elif field_type == 'string':
                return self._render_string_column(column, format_config, effective_field_name)
            elif field_type == 'month_string':
                return self._render_month_string_column(column, format_config, effective_field_name)
            elif field_type == 'year_string':
                return self._render_year_string_column(column, format_config, effective_field_name)
            else:
                self.logger.warning(f"🎨 [格式渲染] 未知字段类型: {field_type}")
                return column
                
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 列格式化失败 {field_name}: {e}")
            return column
    
    def render_value(self, 
                    value: Any, 
                    field_type: str, 
                    field_name: str = None,
                    table_type: str = None) -> str:
        """
        渲染单个值
        
        Args:
            value: 原始值
            field_type: 字段类型
            field_name: 字段名（可选）
            
        Returns:
            格式化后的字符串
        """
        try:
            # 获取格式配置 - 传递表类型信息
            format_config = self.format_config.get_format_rules(field_type, table_type)
            if not format_config:
                return str(value) if value is not None else ""
            
            # 根据字段类型选择渲染方法
            if field_type == 'currency':
                return self._render_currency_value(value, format_config)
            elif field_type == 'integer':
                return self._render_integer_value(value, format_config)
            elif field_type == 'float':
                return self._render_float_value(value, format_config, field_name)
            elif field_type == 'percentage':
                return self._render_percentage_value(value, format_config)
            elif field_type == 'date':
                return self._render_date_value(value, format_config)
            elif field_type == 'string':
                return self._render_string_value(value, format_config, field_name)
            elif field_type == 'month_string':
                return self._render_month_string_value(value, format_config)
            elif field_type == 'year_string':
                return self._render_year_string_value(value, format_config)
            else:
                return str(value) if value is not None else ""
                
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 值格式化失败 {field_name}: {e}")
            return str(value) if value is not None else ""
    
    # ================== 货币类型渲染 ==================
    
    def _render_currency_column(self, 
                               column: pd.Series, 
                               format_config: Dict, 
                               field_name: str) -> pd.Series:
        """渲染货币类型列"""
        try:
            def format_currency(value):
                return self._render_currency_value(value, format_config)
            
            return column.apply(format_currency)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 货币列格式化失败 {field_name}: {e}")
            return column
    
    def _render_currency_value(self, value: Any, format_config: Dict) -> str:
        """渲染货币值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return format_config.get('zero_display', '0.00')
            
            # 转换为数值
            try:
                if isinstance(value, str):
                    # 清理字符串中的非数字字符
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value:
                        return format_config.get('zero_display', '0.00')
                    numeric_value = float(cleaned_value)
                else:
                    numeric_value = float(value)
            except (ValueError, TypeError):
                return format_config.get('zero_display', '0.00')
            
            # 格式化设置
            decimal_places = format_config.get('decimal_places', 2)
            thousand_separator = format_config.get('thousand_separator', ',')
            symbol = format_config.get('symbol', '¥')
            symbol_position = format_config.get('symbol_position', 'prefix')
            negative_format = format_config.get('negative_format', '-{symbol}{value}')
            
            # 格式化数值
            if numeric_value == 0:
                return format_config.get('zero_display', '0.00')
            
            # 格式化为指定小数位数
            formatted_value = f"{abs(numeric_value):,.{decimal_places}f}"
            
            # 替换千分位分隔符
            if thousand_separator != ',':
                formatted_value = formatted_value.replace(',', thousand_separator)
            
            # 添加货币符号
            if symbol_position == 'prefix':
                result = f"{symbol}{formatted_value}"
            else:
                result = f"{formatted_value}{symbol}"
            
            # 处理负数
            if numeric_value < 0:
                result = negative_format.format(symbol=symbol, value=formatted_value)
            
            return result
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 货币值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 整型渲染 ==================
    
    def _render_integer_column(self, 
                              column: pd.Series, 
                              format_config: Dict, 
                              field_name: str) -> pd.Series:
        """渲染整型列"""
        try:
            def format_integer(value):
                return self._render_integer_value(value, format_config)
            
            return column.apply(format_integer)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 整型列格式化失败 {field_name}: {e}")
            return column
    
    def _render_integer_value(self, value: Any, format_config: Dict) -> str:
        """渲染整型值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return format_config.get('zero_display', '0')
            
            # 转换为整数
            try:
                if isinstance(value, str):
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value:
                        return format_config.get('zero_display', '0')
                    numeric_value = int(float(cleaned_value))
                else:
                    numeric_value = int(float(value))
            except (ValueError, TypeError):
                return format_config.get('zero_display', '0')
            
            # 格式化设置
            thousand_separator = format_config.get('thousand_separator', ',')
            
            # 格式化数值
            if numeric_value == 0:
                return format_config.get('zero_display', '0')
            
            # 添加千分位分隔符
            formatted_value = f"{numeric_value:,}"
            
            # 替换千分位分隔符
            if thousand_separator != ',':
                formatted_value = formatted_value.replace(',', thousand_separator)
            
            return formatted_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 整型值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 浮点型渲染 ==================
    
    def _render_float_column(self, 
                            column: pd.Series, 
                            format_config: Dict, 
                            field_name: str) -> pd.Series:
        """渲染浮点型列"""
        try:
            def format_float(value):
                return self._render_float_value(value, format_config, field_name)
            
            return column.apply(format_float)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 浮点型列格式化失败 {field_name}: {e}")
            return column
    
    def _render_float_value(self, value: Any, format_config: Dict, field_name: str = None) -> str:
        """渲染浮点型值 - 增强空值处理"""
        try:
            # 🎯 [用户需求] 扩展空值处理：None、nan、0、0.0、空字符串、空格等统一显示为"0.00"
            
            # 1. 检查pandas的空值类型 - 🎯 [用户需求] 空值也要显示两位小数格式
            if pd.isna(value) or value is None:
                decimal_places = format_config.get('decimal_places', 2)
                return f"{0:.{decimal_places}f}"
            
            # 2. 检查字符串类型的空值情况
            if isinstance(value, str):
                cleaned_str = value.strip()  # 去除前后空格
                # 🎯 [用户需求] 检查空字符串或特殊空值字符串，包括"-"字符
                # 🔧 [P2-修复] 特殊处理车补字段的"-"字符，确保显示为"0.00"
                if (not cleaned_str or 
                    cleaned_str.lower() in ['none', 'nan', 'null', '', ' ', '-'] or
                    (field_name and "车补" in field_name and cleaned_str == '-')):
                    decimal_places = format_config.get('decimal_places', 2)
                    return f"{0:.{decimal_places}f}"
            
            # 3. 转换为浮点数
            try:
                if isinstance(value, str):
                    # 🎯 [用户需求] 特殊处理"-"字符，直接返回"0.00"
                    if value.strip() == '-':
                        decimal_places = format_config.get('decimal_places', 2)
                        return f"{0:.{decimal_places}f}"
                    
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value or cleaned_value == '-':
                        decimal_places = format_config.get('decimal_places', 2)
                        return f"{0:.{decimal_places}f}"
                    numeric_value = float(cleaned_value)
                else:
                    numeric_value = float(value)
                
                # 4. 检查是否为NaN或无穷大
                if pd.isna(numeric_value) or not np.isfinite(numeric_value):
                    decimal_places = format_config.get('decimal_places', 2)
                    return f"{0:.{decimal_places}f}"
                    
            except (ValueError, TypeError, OverflowError):
                decimal_places = format_config.get('decimal_places', 2)
                return f"{0:.{decimal_places}f}"
            
            # 5. 格式化设置 - 🎯 [用户需求] 强制确保所有浮点数字段显示两位小数
            decimal_places = format_config.get('decimal_places', 2)
            
            # 6. 零值处理 - 用户要求0和0.0也显示为"0.00"
            if numeric_value == 0 or numeric_value == 0.0:
                return f"{0:.{decimal_places}f}"  # 确保零值也按decimal_places格式化
            
            # 7. 格式化为指定小数位数 - 🎯 [用户需求] 强制两位小数显示
            formatted_value = f"{numeric_value:.{decimal_places}f}"
            
            return formatted_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 浮点型值格式化失败: {e}")
            # 🎯 [用户需求] 异常情况下也返回两位小数格式的零值显示
            decimal_places = format_config.get('decimal_places', 2)
            return f"{0:.{decimal_places}f}"
    
    # ================== 百分比渲染 ==================
    
    def _render_percentage_column(self, 
                                 column: pd.Series, 
                                 format_config: Dict, 
                                 field_name: str) -> pd.Series:
        """渲染百分比列"""
        try:
            def format_percentage(value):
                return self._render_percentage_value(value, format_config)
            
            return column.apply(format_percentage)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 百分比列格式化失败 {field_name}: {e}")
            return column
    
    def _render_percentage_value(self, value: Any, format_config: Dict) -> str:
        """渲染百分比值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return "0%"
            
            # 转换为数值
            try:
                if isinstance(value, str):
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value:
                        return "0%"
                    numeric_value = float(cleaned_value)
                else:
                    numeric_value = float(value)
            except (ValueError, TypeError):
                return "0%"
            
            # 格式化设置
            decimal_places = format_config.get('decimal_places', 1)
            symbol = format_config.get('symbol', '%')
            multiply_by_100 = format_config.get('multiply_by_100', True)
            
            # 计算百分比值
            if multiply_by_100:
                percentage_value = numeric_value * 100
            else:
                percentage_value = numeric_value
            
            # 格式化
            formatted_value = f"{percentage_value:.{decimal_places}f}{symbol}"
            
            return formatted_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 百分比值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 日期渲染 ==================
    
    def _render_date_column(self, 
                           column: pd.Series, 
                           format_config: Dict, 
                           field_name: str) -> pd.Series:
        """渲染日期列"""
        try:
            def format_date(value):
                return self._render_date_value(value, format_config)
            
            return column.apply(format_date)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 日期列格式化失败 {field_name}: {e}")
            return column
    
    def _render_date_value(self, value: Any, format_config: Dict) -> str:
        """渲染日期值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return ""
            
            # 转换为日期
            try:
                if isinstance(value, str):
                    # 尝试解析字符串日期
                    input_format = format_config.get('input_format', '%Y-%m-%d')
                    date_obj = datetime.strptime(value, input_format)
                elif isinstance(value, (datetime, date)):
                    date_obj = value
                else:
                    return str(value)
            except (ValueError, TypeError):
                return str(value)
            
            # 格式化设置
            display_format = format_config.get('display_format', '%Y年%m月%d日')
            
            # 格式化日期
            formatted_date = date_obj.strftime(display_format)
            
            return formatted_date
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 日期值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 字符串渲染 ==================
    
    def _render_string_column(self, 
                             column: pd.Series, 
                             format_config: Dict, 
                             field_name: str) -> pd.Series:
        """渲染字符串列"""
        try:
            def format_string(value):
                return self._render_string_value(value, format_config, field_name)
            
            return column.apply(format_string)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 字符串列格式化失败 {field_name}: {e}")
            return column
    
    def _render_string_value(self, value: Any, format_config: Dict, field_name: str = None) -> str:
        """渲染字符串值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return format_config.get('empty_display', '')
            
            # 转换为字符串
            str_value = str(value)
            
            # 🎯 [用户需求] 处理浮点数格式的字符串，移除不必要的.0后缀
            # 例如: "19990089.0" -> "19990089"
            if str_value.endswith('.0'):
                try:
                    # 验证这确实是一个整数值
                    float_val = float(str_value)
                    if float_val == int(float_val):  # 确认是整数
                        str_value = str(int(float_val))
                except (ValueError, OverflowError):
                    # 如果转换失败，保持原字符串
                    pass
            
            # 🎯 [用户需求] 人员类别代码特殊处理：数字要显示为两位数（01, 02等）
            if field_name and "人员类别代码" in field_name:
                try:
                    # 尝试转换为整数并零填充到两位
                    if str_value.replace('.', '').replace('-', '').isdigit():
                        numeric_val = int(float(str_value))
                        if 0 <= numeric_val <= 99:  # 合理的人员类别代码范围
                            str_value = f"{numeric_val:02d}"
                except (ValueError, OverflowError):
                    # 如果转换失败，保持原字符串
                    pass
            
            # 格式化设置
            trim_whitespace = format_config.get('trim_whitespace', True)
            max_length = format_config.get('max_length', 100)
            
            # 处理空白字符
            if trim_whitespace:
                str_value = str_value.strip()
            
            # 🎯 [用户需求] 特殊处理"-"字符 - 字符串类型字段中的"-"改为空白
            if str_value == '-':
                return format_config.get('empty_display', '')
            
            # 处理长度限制
            if len(str_value) > max_length:
                str_value = str_value[:max_length-3] + "..."
            
            # 如果处理后为空，返回空值显示
            if not str_value:
                return format_config.get('empty_display', '')
            
            return str_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 字符串值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 月份字符串渲染 ==================
    
    def _render_month_string_column(self, 
                                   column: pd.Series, 
                                   format_config: Dict, 
                                   field_name: str) -> pd.Series:
        """渲染月份字符串列（提取后两位）"""
        try:
            def format_month(value):
                return self._render_month_string_value(value, format_config)
            
            return column.apply(format_month)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 月份字符串列格式化失败 {field_name}: {e}")
            return column
    
    def _render_month_string_value(self, value: Any, format_config: Dict) -> str:
        """渲染月份字符串值（提取后两位）"""
        try:
            # 处理空值
            if pd.isna(value) or value is None or value == '':
                return ""
            
            # 转换为字符串并提取后两位月份
            try:
                month_str = str(value).strip()
                if not month_str:
                    return ""
                
                # 如果是数字格式，提取后两位
                if month_str.isdigit():
                    if len(month_str) >= 2:
                        return month_str[-2:]
                    else:
                        return month_str.zfill(2)  # 不足两位补零
                else:
                    # 如果包含非数字字符，尝试提取数字部分
                    import re
                    digits = re.findall(r'\d+', month_str)
                    if digits:
                        month_num = digits[-1]  # 取最后一个数字序列
                        if len(month_num) >= 2:
                            return month_num[-2:]
                        else:
                            return month_num.zfill(2)
                    else:
                        return month_str  # 无法提取数字，返回原值
                        
            except (ValueError, TypeError):
                return str(value)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 月份字符串值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 年份字符串渲染 ==================
    
    def _render_year_string_column(self, 
                                  column: pd.Series, 
                                  format_config: Dict, 
                                  field_name: str) -> pd.Series:
        """渲染年份字符串列"""
        try:
            def format_year(value):
                return self._render_year_string_value(value, format_config)
            
            return column.apply(format_year)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 年份字符串列格式化失败 {field_name}: {e}")
            return column
    
    def _render_year_string_value(self, value: Any, format_config: Dict) -> str:
        """渲染年份字符串值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None or value == '':
                return ""
            
            # 转换为字符串
            try:
                year_str = str(value).strip()
                if not year_str:
                    return ""
                
                # 如果是数字格式，直接返回
                if year_str.isdigit():
                    return year_str
                else:
                    # 如果包含非数字字符，尝试提取数字部分
                    import re
                    digits = re.findall(r'\d+', year_str)
                    if digits:
                        # 通常年份是4位数，选择最可能的年份
                        for digit in digits:
                            if len(digit) == 4 and digit.startswith(('19', '20')):
                                return digit
                        # 如果没有找到4位年份，返回第一个数字序列
                        return digits[0]
                    else:
                        return year_str  # 无法提取数字，返回原值
                        
            except (ValueError, TypeError):
                return str(value)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 年份字符串值格式化失败: {e}")
            return str(value) if value is not None else ""

    # ================== 工具和状态方法 ==================
    
    def clear_cache(self):
        """清除格式化缓存"""
        try:
            self._format_cache.clear()
            self.logger.debug("🎨 [格式渲染] 缓存已清除")
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 清除缓存失败: {e}")
    
    def get_rendering_statistics(self) -> Dict[str, Any]:
        """获取渲染统计信息"""
        try:
            return {
                'error_count': self._error_count,
                'warning_count': self._warning_count,
                'cache_size': len(self._format_cache),
                'supported_types': [
                    'currency', 'integer', 'float', 'percentage', 'date', 'string'
                ]
            }
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 获取统计信息失败: {e}")
            return {"error": str(e)}
    
    def validate_data_types(self, df: pd.DataFrame, table_type: str) -> Dict[str, List[str]]:
        """
        验证数据类型
        
        Args:
            df: 数据框
            table_type: 表格类型
            
        Returns:
            验证结果字典 {status: [messages]}
        """
        try:
            result = {
                'valid': [],
                'warnings': [],
                'errors': []
            }
            
            field_types = self.field_registry.get_table_field_types(table_type)
            
            for column in df.columns:
                if column not in field_types:
                    result['warnings'].append(f"未定义字段类型: {column}")
                    continue
                
                field_type = field_types[column]
                
                # 验证数据类型一致性
                if field_type in ['currency', 'integer', 'float']:
                    non_numeric_count = 0
                    for value in df[column].dropna():
                        try:
                            float(str(value).replace(',', '').replace('¥', ''))
                        except (ValueError, TypeError):
                            non_numeric_count += 1
                    
                    if non_numeric_count > 0:
                        result['warnings'].append(
                            f"字段 {column} 包含 {non_numeric_count} 个非数值数据"
                        )
                    else:
                        result['valid'].append(f"字段 {column} 类型验证通过")
                else:
                    result['valid'].append(f"字段 {column} 类型验证通过")
            
            return result
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 数据类型验证失败: {e}")
            return {"errors": [str(e)]}
    
    def reset_statistics(self):
        """重置统计信息"""
        try:
            self._error_count = 0
            self._warning_count = 0
            self.logger.debug("🎨 [格式渲染] 统计信息已重置")
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 重置统计信息失败: {e}")